"use client";
import React, { useState, useMemo } from "react";
import JobCard from "../Cards/JobCard";
import { useGetSkills } from "@/hooks/useQuery";
import { useGetAllJobs } from "@/hooks/useQuery";

const JobListingHome = () => {
  const [activeTag, setActiveTag] = useState<string>("ALL");

  // Fetch skills from API
  const { data: skillsData } = useGetSkills({
    limit: 50, // Fetch more skills to have a good selection for random picking
  });

  // Create jobsTags array with "ALL" first, then 8 random skills
  const jobsTags = useMemo(() => {
    if (!skillsData?.data?.skills) return ["ALL"];

    const skills = skillsData.data.skills;
    // Shuffle and take 8 random skills
    const shuffledSkills = [...skills].sort(() => Math.random() - 0.5).slice(0, 8);

    return ["ALL", ...shuffledSkills.map((skill) => skill.skill)];
  }, [skillsData]);

  // Fetch jobs based on selected skill
  const jobSearchParams = useMemo(() => {
    return {
      limit: 6, // Only show 6 jobs
      ...(activeTag !== "ALL" && { search: activeTag }), // Only add search if not "ALL"
    };
  }, [activeTag]);

  const { data: jobsData, isLoading: jobsLoading } = useGetAllJobs(jobSearchParams);

  // Get jobs from API data
  const jobs = jobsData?.data?.allJobs || [];

  return (
    <>
      <ul className="flex flex-wrap gap-3 justify-center py-20">
        {jobsTags.map((tag, index) => (
          <li
            key={index}
            className={`text-gray-100 border rounded-full px-6 py-2 border-gray-100 cursor-pointer ${
              activeTag === tag ? "text-orange-100 border-orange-100" : ""
            }`}
            onClick={() => setActiveTag(tag)}
          >
            {tag}
          </li>
        ))}
      </ul>

      {/* Loading state */}
      {jobsLoading && (
        <div className="flex justify-center py-10">
          <div className="text-gray-100">Loading jobs...</div>
        </div>
      )}

      {/* Jobs grid */}
      {!jobsLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {jobs.map((job, index) => (
            <div key={job._id || index} className="h-full">
              <JobCard
                companyName={job.recruiterProfile?.companyProfile?.companyName || "Company"}
                detailLink={`/job/${job._id}`}
                imageUrl={
                  job.recruiterProfile?.companyProfile?.profilePicture || "/images/company3.png"
                }
                jobTags={job.skillsTag || []}
                jobTitle={job.jobTitle}
                location={job.location?.city || "Location not specified"}
                payRate={job.salaryRangeStart?.toString() || "0"}
                timePeriod={job.salaryType}
              />
            </div>
          ))}
        </div>
      )}

      {/* No jobs found state */}
      {!jobsLoading && jobs.length === 0 && (
        <div className="flex justify-center py-10">
          <div className="text-gray-100">No jobs found for the selected skill.</div>
        </div>
      )}
    </>
  );
};

export default JobListingHome;
